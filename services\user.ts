import { CreditsAmount, CreditsTransType } from "./credit";
import { findUserByEmail, findUserByUuid, insertUser, getUserUuidsByEmail } from "@/models/user";

import { User } from "@/types/user";
import { auth } from "@/auth";
import { getOneYearLaterTimestr } from "@/lib/time";
import { getUserUuidByApiKey } from "@/models/apikey";
import { headers } from "next/headers";
import { increaseCredits } from "./credit";

export async function saveUser(user: User) {
  try {
    const existUser = await findUserByEmail(user.email);
    if (!existUser) {
      await insertUser(user);

      // increase credits for new user, expire in one year
      await increaseCredits({
        user_uuid: user.uuid || "",
        trans_type: CreditsTransType.NewUser,
        credits: CreditsAmount.NewUserGet,
        expired_at: getOneYearLaterTimestr(),
      });
    } else {
      user.id = existUser.id;
      user.uuid = existUser.uuid;
      user.created_at = existUser.created_at;
    }

    return user;
  } catch (e) {
    console.log("save user failed: ", e);
    throw e;
  }
}

export async function getUserUuid() {
  let user_uuid = "";

  const token = getBearerToken();

  if (token) {
    // api key
    if (token.startsWith("sk-")) {
      const uuid = await getUserUuidByApiKey(token);
      if (uuid) {
        console.log("Got user UUID from API key");
        return uuid;
      }
    }
  }

  const session = await auth();
  if (session && session.user) {
    // 直接从会话中获取 UUID
    if (session.user.uuid) {
      console.log("Got user UUID from session");
      user_uuid = session.user.uuid;
    } 
    // 如果会话中没有 UUID 但有 email，通过 email 查询 UUID
    else if (session.user.email) {
      console.log("Trying to get UUID by email from session:", session.user.email);
      const uuids = await getUserUuidsByEmail(session.user.email);
      if (uuids && uuids.length > 0) {
        console.log("Got user UUID by email from database");
        user_uuid = uuids[0];
      }
    }
  }

  return user_uuid;
}

export function getBearerToken() {
  const h = headers();
  const auth = h.get("Authorization");
  if (!auth) {
    return "";
  }

  return auth.replace("Bearer ", "");
}

export async function getUserEmail() {
  let user_email = "";

  const session = await auth();
  if (session && session.user && session.user.email) {
    user_email = session.user.email;
  }

  return user_email;
}

export async function getUserInfo() {
  let user_uuid = await getUserUuid();

  if (!user_uuid) {
    return;
  }

  const user = await findUserByUuid(user_uuid);

  return user;
}
