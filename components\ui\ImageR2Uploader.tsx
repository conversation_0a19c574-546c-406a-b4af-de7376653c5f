"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { Upload, Check, Loader2 } from "lucide-react";

interface ImageR2UploaderProps {
  sourceUrl: string;
  filename: string;
  onUploadComplete?: (url: string) => void;
}

export const ImageR2Uploader = ({ 
  sourceUrl = "https://tempfile.aiquickdraw.com/v/c83fce51-56f5-4475-8f84-6efe4d5501bf_0.png", 
  filename = "c83fce51-56f5-4475-8f84-6efe4d5501bf_0.png",
  onUploadComplete
}: ImageR2UploaderProps) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleUpload = async () => {
    setIsUploading(true);
    setError(null);
    
    try {
      // Use the API endpoint instead of direct R2 upload
      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceUrl,
          filename,
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }
      
      setUploadedUrl(data.url);
      toast.success(data.message || "Image uploaded successfully");
      
      if (onUploadComplete) {
        onUploadComplete(data.url);
      }
    } catch (err: any) {
      console.error("Upload failed:", err);
      setError(err.message || "Failed to upload image");
      toast.error("Failed to upload image");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Card className="p-4 flex flex-col items-center justify-center space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-medium">Upload Image to R2</h3>
        <p className="text-sm text-muted-foreground">
          {uploadedUrl 
            ? "Image has been uploaded successfully" 
            : `Upload ${filename} to R2 storage`}
        </p>
      </div>
      
      {error && (
        <div className="text-sm text-destructive">{error}</div>
      )}
      
      {uploadedUrl ? (
        <div className="flex items-center justify-center text-primary space-x-2">
          <Check className="w-5 h-5" />
          <span>Upload Complete</span>
        </div>
      ) : (
        <Button 
          onClick={handleUpload} 
          disabled={isUploading}
          className="flex items-center space-x-2"
        >
          {isUploading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Uploading...</span>
            </>
          ) : (
            <>
              <Upload className="w-4 h-4" />
              <span>Upload to R2</span>
            </>
          )}
        </Button>
      )}
      
      {uploadedUrl && (
        <div className="w-full overflow-hidden text-ellipsis text-xs text-muted-foreground">
          <span className="font-medium">Image URL: </span>
          <a 
            href={uploadedUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-primary hover:underline"
          >
            {uploadedUrl}
          </a>
        </div>
      )}
    </Card>
  );
};

export default ImageR2Uploader; 