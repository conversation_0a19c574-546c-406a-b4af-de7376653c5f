import { NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";

export const runtime = 'edge';

export async function POST(req: Request, { params }: { params: { callbackId: string } }) {
  try {
    const callbackId = params.callbackId;
    
    if (!callbackId) {
      return NextResponse.json(
        { error: "Callback ID is required" },
        { status: 400 }
      );
    }

    const data = await req.json();
    console.log(`Received Kontext callback, ID ${callbackId}:`, data);

    // Check callback data
    if (!data || !data.imageUrl) {
      return NextResponse.json(
        { error: "Invalid callback data" },
        { status: 400 }
      );
    }

    // Update task status in database
    const supabase = getSupabaseClient();
    const { error } = await supabase
      .from("kontext_generations")
      .update({
        status: "completed",
        completed_at: new Date().toISOString(),
        generated_image_url: data.imageUrl
      })
      .eq("callback_id", callbackId);

    if (error) {
      console.error("Error updating Kontext generation record:", error);
      return NextResponse.json(
        { error: "Failed to update generation record" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Callback processed successfully"
    });
  } catch (error) {
    console.error("Error processing Kontext callback:", error);
    return NextResponse.json(
      { error: "Failed to process callback" },
      { status: 500 }
    );
  }
} 