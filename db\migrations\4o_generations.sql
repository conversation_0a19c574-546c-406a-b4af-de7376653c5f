-- 创建4o_generations表
CREATE TABLE IF NOT EXISTS "4o_generations" (
  "id" SERIAL PRIMARY KEY,
  "user_uuid" VARCHAR NOT NULL,
  "nickname" VARCHAR,
  "email" VARCHAR,
  "task_id" VARCHAR NOT NULL,
  "callback_id" VARCHAR NOT NULL,
  "original_image_url" VARCHAR NOT NULL,
  "generated_image_url" VARCHAR,
  "prompt" TEXT NOT NULL,
  "status" VARCHAR NOT NULL DEFAULT 'pending',
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL,
  "completed_at" TIMESTAMP WITH TIME ZONE,
  UNIQUE("task_id"),
  UNIQUE("callback_id")
);

-- 创建索引
CREATE INDEX IF NOT EXISTS "4o_generations_user_uuid_idx" ON "4o_generations" ("user_uuid");
CREATE INDEX IF NOT EXISTS "4o_generations_task_id_idx" ON "4o_generations" ("task_id");
CREATE INDEX IF NOT EXISTS "4o_generations_status_idx" ON "4o_generations" ("status"); 