import { NextResponse } from "next/server";

export const runtime = 'edge';

export async function POST(req: Request) {
  try {
    console.log('Translation API called');
    
    const { text } = await req.json();
    console.log('Input text to translate:', text);

    if (!text) {
      return NextResponse.json(
        { error: "Text is required" },
        { status: 400 }
      );
    }

    // 调用DeepSeek API进行翻译
    console.log('Calling DeepSeek API for translation...');
    try {
      const apiKey = process.env.DEEPSEEK_API_KEY;
      
      if (!apiKey) {
        throw new Error("DEEPSEEK_API_KEY is not configured");
      }
      
      const response = await fetch("https://api.deepseek.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "deepseek-chat",
          messages: [
            {
              role: "system",
              content: "You are an English translator. You only speak English. You translate all languages into English. You will translate the received text content into English. Only return the translated text without any explanations or additional content."
            },
            {
              role: "user",
              content: text
            }
          ],
          temperature: 0.1
        })
      });
      
      console.log('DeepSeek API response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('DeepSeek API raw response:', JSON.stringify(data).substring(0, 200));
      
      // 从DeepSeek响应中提取翻译文本
      const translatedText = data.choices[0].message.content.trim();
      
      console.log('Translated text:', translatedText);

      return NextResponse.json({ translatedText });
    } catch (error: any) {
      console.error('DeepSeek API call failed:', error);
      
      // 如果DeepSeek API调用失败，直接返回原文
      console.log('API call failed, returning original text');
      return NextResponse.json({ translatedText: text });
    }
  } catch (error: any) {
    console.error("Translation error:", error);
    return NextResponse.json(
      { error: error.message || "Failed to translate text" },
      { status: 500 }
    );
  }
} 