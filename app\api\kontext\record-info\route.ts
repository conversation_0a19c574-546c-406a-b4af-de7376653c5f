import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getSupabaseClient } from "@/models/db";

export const runtime = 'edge';

// Kie.ai Flux Kontext API configuration
const KIE_API_URL = "https://kieai.erweima.ai/api/v1/flux/kontext/record-info";

export async function GET(req: Request) {
  try {
    // Get URL parameters
    const url = new URL(req.url);
    const taskId = url.searchParams.get("taskId");
    
    // Ensure environment variables are correctly loaded
    const KIE_API_KEY = process.env.KIE_API_KEY;
    if (!KIE_API_KEY) {
      console.error('ERROR: KIE_API_KEY environment variable is not set');
      return NextResponse.json(
        { error: "API configuration error", message: "Server configuration error" },
        { status: 500 }
      );
    }
    
    console.log('Checking environment variables in record-info GET request:');
    console.log('- KIE_API_KEY exists:', !!process.env.KIE_API_KEY);
    console.log('- KIE_API_KEY value (first 4 chars):', KIE_API_KEY ? `${KIE_API_KEY.substring(0, 4)}...` : 'not set');
    
    // Get task ID and log
    console.log('Original task ID:', taskId);
    
    if (!taskId) {
      return NextResponse.json(
        { error: "Task ID is required" },
        { status: 400 }
      );
    }
    
    // Add cache control headers
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };

    // First try to get completed task information from database
    try {
      const userUuid = await getUserUuid();
      const supabase = getSupabaseClient();
      
      const { data: generationRecord, error } = await supabase
        .from("4o_generations")
        .select("*")
        .eq("task_id", taskId)
        .eq("user_uuid", userUuid)
        .single();
      
      console.log('Database query result:', error ? 'error' : 'success');
      if (error) {
        console.error('Database query error:', error);
      } else {
        console.log('Database record:', generationRecord);
        
        // If the record in the database is already marked as completed, return the result directly
        if (generationRecord && 
            generationRecord.status === "COMPLETED" && 
            generationRecord.generated_image_url) {
          console.log('Found completed task record in database');
          
          return NextResponse.json({
            data: {
              status: "COMPLETED",
              progress: "1.0",
              successFlag: 1,
              response: {
                resultUrls: [generationRecord.generated_image_url]
              }
            },
            taskId: taskId,
            message: "Task completed (from database)"
          }, { headers });
        }
      }
    } catch (dbError) {
      console.error('Error querying database:', dbError);
      // Continue to check API status
    }

    // Try different task ID formats
    const taskIdVariations = [
      taskId,
      taskId.startsWith('fluxkontext_') ? taskId : `fluxkontext_${taskId}`,
      taskId.includes('fluxkontext_') ? taskId.replace('fluxkontext_', '') : taskId
    ];
    
    console.log('Will try the following task ID formats:', taskIdVariations);

    // Try all task ID variations
    let response = null;
    let responseData = null;
    let successfulTaskId = null;
    
    for (const currentTaskId of taskIdVariations) {
      const apiUrl = `${KIE_API_URL}?taskId=${currentTaskId}`;
      console.log('Trying API URL:', apiUrl);
      
      try {
        const currentResponse = await fetch(apiUrl, {
          method: "GET",
          headers: {
            "Accept": "application/json",
            "Authorization": `Bearer ${KIE_API_KEY}`,
            ...headers
          }
        });
        
        console.log(`Task ID [${currentTaskId}] response status code:`, currentResponse.status);
        
        if (currentResponse.ok) {
          const responseText = await currentResponse.text();
          try {
            const parsedData = JSON.parse(responseText);
            console.log(`Task ID [${currentTaskId}] response successful:`, JSON.stringify(parsedData).substring(0, 200) + '...');
            
            response = currentResponse;
            responseData = parsedData;
            successfulTaskId = currentTaskId;
            break; // Found valid response, exit loop
          } catch (jsonError) {
            console.error(`Task ID [${currentTaskId}] JSON parsing error:`, jsonError);
            continue;
          }
        } else {
          console.log(`Task ID [${currentTaskId}] request failed, status code: ${currentResponse.status}`);
          
          // Try to parse error response
          try {
            const errorText = await currentResponse.text();
            console.log(`Task ID [${currentTaskId}] error response:`, errorText);
          } catch (e) {
            console.error(`Task ID [${currentTaskId}] unable to read error response:`, e);
          }
        }
      } catch (fetchError) {
        console.error(`Task ID [${currentTaskId}] request error:`, fetchError);
      }
    }

    // If all variations fail, and database has task record but not completed, return in-progress status
    if (!response) {
      try {
        const userUuid = await getUserUuid();
        const supabase = getSupabaseClient();
        
        const { data: pendingRecord, error } = await supabase
          .from("4o_generations")
          .select("*")
          .eq("task_id", taskId)
          .eq("user_uuid", userUuid)
          .single();
        
        if (!error && pendingRecord) {
          console.log('Unable to get task status from API, but found task record in database');
          
          // Update task status to in-progress
          const minutesSinceCreation = pendingRecord.created_at ? 
            (Date.now() - new Date(pendingRecord.created_at).getTime()) / (1000 * 60) : 0;
          
          console.log('Minutes since task creation:', minutesSinceCreation);
          
          // Check if task has been running for more than 10 minutes, if so, assume it's completed but API has issues
          if (minutesSinceCreation > 10) {
            // When task has been running for more than 10 minutes and API returns 404, API might have issues
            // But task might be completed, so we give a special hint
            console.log('Task has been running for more than 10 minutes, might be completed but API has issues');
            
            return NextResponse.json({
              data: {
                status: "POSSIBLE_COMPLETED",
                progress: "0.9",
                successFlag: 0,
                errorCode: 404,
                errorMessage: "Task might be completed but API status check failed",
                response: {
                  resultUrls: []
                }
              },
              taskId: taskId,
              message: "Task status unknown, but might be completed"
            }, { headers });
          }
          
          // Otherwise return in-progress status
          return NextResponse.json({
            data: {
              status: "GENERATING",
              progress: "0.5",
              successFlag: 0,
              response: {
                resultUrls: []
              }
            },
            taskId: taskId,
            message: "Task is still processing"
          }, { headers });
        }
      } catch (fallbackError) {
        console.error('Error during fallback check:', fallbackError);
      }
      
      // If all attempts fail, return a more friendly error
      return NextResponse.json(
        { 
          data: {
            status: "ERROR",
            progress: "0",
            successFlag: 3,
            errorCode: 404,
            errorMessage: "Unable to get task status",
            response: {
              resultUrls: []
            }
          },
          taskId: taskId,
          message: "Unable to get task status, please try again later",
          allTriedTaskIds: taskIdVariations
        },
        { status: 200 } // Return 200 instead of 404 to avoid frontend treating it as a failure
      );
    }

    // Extract and unify status
    let status = responseData.status;
    let progress = responseData.progress || "0";
    let images = [];
    
    if (status === "COMPLETED" || status === "SUCCESS") {
      status = "COMPLETED"; // Unify status name
      
      // Try to extract images from different locations
      if (responseData.result?.images?.length > 0) {
        images = responseData.result.images;
      } else if (responseData.images?.length > 0) {
        images = responseData.images;
      } else if (responseData.resultUrls?.length > 0) {
        images = responseData.resultUrls;
      } else if (responseData.response?.resultImageUrl) {
        // Handle original API response structure
        images = [responseData.response.resultImageUrl];
      }
      
      console.log('Found generated images:', images);
      
      // If task is completed, update database
      if (images.length > 0) {
        try {
          const userUuid = await getUserUuid();
          const supabase = getSupabaseClient();
          await supabase
            .from("4o_generations")
            .update({
              status: "COMPLETED",
              completed_at: new Date().toISOString(),
              generated_image_url: images[0]
            })
            .eq("task_id", taskId)
            .eq("user_uuid", userUuid);
            
          console.log('Database update successful');
        } catch (error) {
          console.error("Error updating database:", error);
          // Continue execution, don't affect return result
        }
      }
    }
    
    // Pass the original API response directly to frontend, but keep our enhancements
    let formattedResponse;
    
    // Check if it's standard Kontext API response format (code/msg/data structure)
    if (responseData.code === 200 && responseData.msg === "success" && responseData.data) {
      // Keep original response structure, but add our own processing results
      formattedResponse = {
        code: responseData.code,
        msg: responseData.msg,
        data: responseData.data,
        // Add the following fields to be compatible with our frontend code
        taskId: taskId,
        actualTaskId: successfulTaskId,
        status: status,
        progress: progress,
        successFlag: status === "COMPLETED" ? 1 : (status === "FAILED" ? 3 : 0),
        response: {
          resultUrls: images
        }
      };
    } else {
      // If not standard format, use our own format
      formattedResponse = {
        data: {
          status: status,
          progress: progress,
          successFlag: status === "COMPLETED" ? 1 : (status === "FAILED" ? 3 : 0),
          errorCode: response.status !== 200 ? response.status : undefined,
          errorMessage: responseData.error || responseData.message,
          response: {
            resultUrls: images
          }
        },
        taskId: taskId,
        originalResponse: responseData,
        actualTaskId: successfulTaskId
      };
    }

    // Return formatted response
    return NextResponse.json(formattedResponse, { headers });
    
  } catch (error) {
    console.error("Error getting task information:", error);
    return NextResponse.json(
      { 
        error: "Failed to get task information", 
        message: (error as Error).message,
        data: {
          status: "ERROR",
          progress: "0",
          successFlag: 3,
          errorMessage: (error as Error).message,
          response: { resultUrls: [] }
        }
      },
      { status: 200 } // Return 200 instead of 500 to avoid frontend treating it as a failure
    );
  }
} 