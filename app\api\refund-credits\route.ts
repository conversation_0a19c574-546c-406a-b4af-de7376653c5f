import { NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";

export const runtime = 'edge';

export async function POST(req: Request) {
  try {
    // 获取请求参数
    const { credits = 3, taskId, reason = 'generation_failed' } = await req.json();
    
    // 验证必要参数
    if (!taskId) {
      return NextResponse.json(
        { error: "任务ID不能为空" },
        { status: 400 }
      );
    }
    
    // 获取用户ID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "未授权操作" },
        { status: 401 }
      );
    }
    
    // 连接数据库
    const supabase = getSupabaseClient();
    
    // 1. 验证该任务是否存在且属于当前用户
    const { data: taskRecord, error: taskError } = await supabase
      .from('4o_generations')
      .select('id, task_id, status')
      .eq('task_id', taskId)
      .eq('user_uuid', userUuid)
      .maybeSingle();
      
    if (taskError || !taskRecord) {
      console.error('查询任务记录失败或任务不存在:', taskError);
      return NextResponse.json(
        { error: "未找到相关任务记录" },
        { status: 404 }
      );
    }
    
    // 更新任务状态为失败
    await supabase
      .from('4o_generations')
      .update({
        status: 'FAILED',
        error_message: reason
      })
      .eq('id', taskRecord.id);
      
    // 2. 检查是否已经为此任务退还过积分
    const { data: refundRecord, error: refundError } = await supabase
      .from('4o_credits_logs')
      .select('id')
      .eq('task_id', taskId)
      .eq('operation', 'refund')
      .maybeSingle();
      
    if (refundRecord) {
      console.log('已为该任务退还过积分，跳过重复退还');
      
      // 查询用户当前积分
      const { data: userCredits } = await supabase
        .from('4o_users')
        .select('left_credits')
        .eq('uuid', userUuid)
        .single();
        
      return NextResponse.json({
        success: true,
        message: "已为该任务退还过积分，无需重复退还",
        creditsLeft: userCredits?.left_credits || 0
      });
    }
    
    // 3. 添加积分记录
    const { error: logError } = await supabase
      .from('4o_credits_logs')
      .insert({
        user_uuid: userUuid,
        credits: credits,
        operation: 'refund', // 表示退还积分
        task_id: taskId,
        description: `退还积分：任务 ${taskId} 失败，原因: ${reason}`,
        created_at: new Date().toISOString()
      });
      
    if (logError) {
      console.error('添加积分记录失败:', logError);
      return NextResponse.json(
        { error: "添加退款记录失败" },
        { status: 500 }
      );
    }
    
    // 4. 更新用户积分
    const { data: userData, error: updateError } = await supabase.rpc(
      'increase_user_credits',
      { user_id: userUuid, increment_amount: credits }
    );
    
    if (updateError) {
      console.error('更新用户积分失败:', updateError);
      return NextResponse.json(
        { error: "更新用户积分失败" },
        { status: 500 }
      );
    }
    
    console.log(`成功为用户 ${userUuid} 退还 ${credits} 积分，任务ID: ${taskId}`);
    
    // 查询用户当前积分
    const { data: userCredits } = await supabase
      .from('4o_users')
      .select('left_credits')
      .eq('uuid', userUuid)
      .single();
    
    return NextResponse.json({
      success: true,
      message: `成功退还 ${credits} 积分`,
      creditsLeft: userCredits?.left_credits || 0
    });
    
  } catch (error) {
    console.error('退还积分过程中出错:', error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 