import { NextResponse } from "next/server";
import Replicate from "replicate";
import { v4 as uuidv4 } from 'uuid';
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";
import { decreaseCredits, CreditsTransType, getUserCredits } from "@/services/credit";

export const runtime = 'edge';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// 创建S3客户端用于R2上传
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

interface Prediction {
  id: string;
  status: string;
  error?: string;
  output?: string[] | string;
}

// 从URL下载图像并上传到R2
async function uploadImageToR2(imageUrl: string, filename: string): Promise<string> {
  try {
    // 下载图像
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }
    
    // 获取图像数据并转换为Uint8Array
    const imageBuffer = await response.arrayBuffer();
    const uint8Array = new Uint8Array(imageBuffer);
    
    // 上传到R2
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET || "",
      Key: filename,
      Body: uint8Array,
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);
    
    // 构建新的URL
    const newUrl = `https://img.flux-krea.dev/${filename}`;
    
    return newUrl;
  } catch (error) {
    // 上传失败但不中断整个流程，返回原始URL
    console.log('Falling back to original URL:', imageUrl);
    return imageUrl;
  }
}

// 将生成任务记录保存到数据库
async function saveGenerationRecord(userUuid: string, taskId: string, prompt: string, aspectRatio: string): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    
    // 生成一个唯一的callback_id
    const callbackId = `callback_${uuidv4()}`;
    
    // 获取用户信息（nickname和email）
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("nickname, email")
      .eq("uuid", userUuid)
      .single();
      
    if (userError) {
      console.error('Error getting user data:', userError);
    }
    
    // 准备数据
    const recordData = {
      user_uuid: userUuid,
      task_id: taskId,
      callback_id: callbackId,
      prompt: prompt,
      original_image_url: '', // Flux Krea 不需要输入图像
      nickname: userData?.nickname || null,
      email: userData?.email || null,
      status: 'pending',
      created_at: new Date().toISOString(),
    };
    
    console.log('Saving Flux Krea record data:', {
      ...recordData,
      prompt: recordData.prompt.substring(0, 30) + '...'
    });
    
    // 插入数据
    const { data, error } = await supabase
      .from('4o_generations')
      .insert(recordData)
      .select();
      
    if (error) {
      console.error('Error saving Flux Krea generation record:', error);
      return false;
    }
    
    console.log('Flux Krea generation record saved successfully:', data);
    return true;
  } catch (error) {
    console.error('Error in saveGenerationRecord:', error);
    return false;
  }
}

export async function POST(req: Request) {
  try {
    const { 
      prompt, 
      aspect_ratio = "1:1", 
      seed, 
      watermark = true, 
      display_public = true, 
      image_count = 1, 
      task_id: requestTaskId 
    } = await req.json();

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required", status: 'failed', taskId: requestTaskId },
        { status: 400 }
      );
    }

    // 获取用户ID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "User authentication required", status: 'failed', taskId: requestTaskId },
        { status: 401 }
      );
    }

    // 确保有有效的任务ID
    const taskId = requestTaskId || `fluxkrea_${uuidv4()}`;

    // 检查用户积分
    const userCredits = await getUserCredits(userUuid);
    const requiredCredits = 3 * image_count; // 每张图片需要3积分

    if (userCredits.left_credits < requiredCredits) {
      return NextResponse.json(
        {
          error: `Insufficient credits. Required: ${requiredCredits}, Available: ${userCredits.left_credits}`,
          status: 'failed',
          taskId: taskId
        },
        { status: 402 }
      );
    }

    // 扣除积分
    try {
      await decreaseCredits({
        user_uuid: userUuid,
        trans_type: CreditsTransType.ImageGeneration,
        credits: requiredCredits
      });
      console.log(`Credits deducted successfully: ${requiredCredits}`);
    } catch (creditError) {
      console.error('Failed to deduct credits:', creditError);
      return NextResponse.json(
        {
          error: "Failed to deduct credits",
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    console.log('Starting Flux Krea generation with params:', {
      task_id: taskId,
      prompt: prompt,
      aspect_ratio: aspect_ratio,
      seed: seed,
      watermark: watermark,
      display_public: display_public,
      image_count: image_count,
      credits_deducted: requiredCredits
    });

    // 在生成之前保存记录到数据库
    try {
      await saveGenerationRecord(userUuid, taskId, prompt, aspect_ratio);
    } catch (dbError) {
      console.error('Failed to save initial record to database:', dbError);
    }

    // 创建预测任务 - 使用 Flux.1 [dev] 模型
    let prediction;
    try {
      const input: any = {
        prompt: prompt,
        aspect_ratio: aspect_ratio,
        output_format: "png",
        output_quality: 80,
        num_inference_steps: 28,
        guidance_scale: 3.5,
        num_outputs: image_count
      };

      // 如果提供了种子，添加到输入中
      if (seed && seed.trim()) {
        input.seed = parseInt(seed);
      }

      prediction = await replicate.predictions.create({
        model: "black-forest-labs/flux-dev",
        input: input
      }) as Prediction;
    } catch (replicateError) {
      console.error('Replicate API error:', replicateError);
      return NextResponse.json(
        { 
          error: replicateError instanceof Error ? replicateError.message : "Failed to connect to generation service",
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    // 轮询检查预测状态
    let finalPrediction = prediction;
    try {
      while (finalPrediction.status !== "succeeded" && finalPrediction.status !== "failed") {
        await new Promise(resolve => setTimeout(resolve, 1000));
        finalPrediction = await replicate.predictions.get(prediction.id) as Prediction;
      }
    } catch (pollingError) {
      console.error('轮询状态错误:', pollingError);
      return NextResponse.json(
        { 
          error: "Failed to check generation status", 
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    if (finalPrediction.status === "failed") {
      const errorMessage = finalPrediction.error || "Generation failed";
      console.error('Generation failed:', errorMessage);
      return NextResponse.json(
        { 
          error: errorMessage, 
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    // 处理输出 URL
    let outputUrls: string[] = [];
    
    if (Array.isArray(finalPrediction.output)) {
      outputUrls = finalPrediction.output.filter(url => typeof url === 'string' && url.startsWith('http'));
    } else if (typeof finalPrediction.output === 'string' && finalPrediction.output.startsWith('http')) {
      outputUrls = [finalPrediction.output];
    }

    if (outputUrls.length === 0) {
      return NextResponse.json(
        { 
          error: "No valid output generated", 
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    // 上传图像到 R2 存储
    const uploadedUrls: string[] = [];
    const originalUrls: string[] = [];

    for (let i = 0; i < outputUrls.length; i++) {
      const outputUrl = outputUrls[i];
      originalUrls.push(outputUrl);
      
      // 生成文件名
      const uuid = uuidv4();
      const filename = `flux-krea-dev-${uuid}-${i + 1}.png`;
      
      try {
        const r2ImageUrl = await uploadImageToR2(outputUrl, filename);
        uploadedUrls.push(r2ImageUrl);
      } catch (uploadError) {
        console.error(`Upload failed for image ${i + 1}:`, uploadError);
        uploadedUrls.push(outputUrl); // 使用原始URL作为回退
      }
    }

    // 更新数据库记录
    try {
      const supabase = getSupabaseClient();
      
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("nickname, email")
        .eq("uuid", userUuid)
        .single();
      
      if (userError) {
        console.error('Error getting user data for record update:', userError);
      }
      
      const updateData = {
        generated_image_url: uploadedUrls[0], // 使用第一张图片的URL
        status: 'COMPLETED',
        completed_at: new Date().toISOString(),
        nickname: userData?.nickname || null,
        email: userData?.email || null
      };

      const { error: updateError } = await supabase
        .from("4o_generations")
        .update(updateData)
        .eq("task_id", taskId)
        .eq("user_uuid", userUuid);
        
      if (updateError) {
        console.error('Error updating generation record status:', updateError);
      } else {
        console.log('Generation record status updated successfully');
      }
    } catch (dbError) {
      console.error('Failed to update generation record status:', dbError);
    }

    // 返回结果
    return NextResponse.json({
      status: 'succeeded',
      images: uploadedUrls,
      original_images: originalUrls,
      taskId: taskId
    });

  } catch (error) {
    console.error("Flux Krea generation error:", error);
    const taskId = (await req.json()).taskId || null;
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to generate image",
        status: 'failed',
        taskId: taskId
      },
      { status: 500 }
    );
  }
}
