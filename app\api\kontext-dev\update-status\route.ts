import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getSupabaseClient } from "@/models/db";

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    // Parse request body
    const requestBody = await request.json();
    const { taskId, status, generatedImageUrl, completed_at, includeUserInfo = true } = requestBody;
    
    console.log('FluxKreaDev update status request:', { 
      taskId, 
      status, 
      generatedImageUrl: generatedImageUrl?.substring(0, 50) + '...',
      completed_at,
      includeUserInfo
    });
    
    // Validate required parameters
    if (!taskId) {
      return NextResponse.json(
        { error: "Task ID is required" },
        { status: 400 }
      );
    }
    
    // Get user UUID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "User authentication failed" },
        { status: 401 }
      );
    }
    
    // Connect to database
    const supabase = getSupabaseClient();
    
    // Try different task ID formats
    const taskIdVariations = [
      taskId,
      taskId.startsWith('kreadev_') ? taskId : `kreadev_${taskId}`,
      taskId.includes('kreadev_') ? taskId.replace('kreadev_', '') : taskId
    ];
    
    let updateSuccess = false;
    let updateResult = null;
    
    // 获取用户信息（nickname和email）
    let userData = null;
    let userError = null;
    
    if (includeUserInfo) {
      try {
        const userResponse = await supabase
          .from("users")
          .select("nickname, email")
          .eq("uuid", userUuid)
          .single();
        
        userData = userResponse.data;
        userError = userResponse.error;
        
        if (userError) {
          console.error('Error getting user data:', userError);
        } else if (userData) {
          console.log(`Successfully got user info: nickname=${userData.nickname}, email=${userData.email}`);
        }
      } catch (error) {
        console.error('Error getting user data:', error);
      }
    }
    
    // Try updating records using different task ID formats
    for (const currentTaskId of taskIdVariations) {
      console.log(`Trying to update record with task ID format [${currentTaskId}]`);
      
      // Check if record exists
      const { data: existingRecord, error: checkError } = await supabase
        .from("4o_generations")
        .select("id, status, nickname, email")
        .eq("task_id", currentTaskId)
        .eq("user_uuid", userUuid)
        .maybeSingle();
        
      if (checkError) {
        console.error(`Error querying record with task ID [${currentTaskId}]:`, checkError);
        continue;
      }
      
      if (!existingRecord) {
        console.log(`No record found for task ID [${currentTaskId}]`);
        continue;
      }
      
      console.log(`Found record for task ID [${currentTaskId}], current status: ${existingRecord.status}`);
      
      // If record is already in completed status, skip update
      if (existingRecord.status === 'COMPLETED') {
        console.log(`Record for task ID [${currentTaskId}] is already in completed status, skipping update`);
        updateSuccess = true;
        updateResult = { id: existingRecord.id, status: existingRecord.status };
        break;
      }
      
      // Update record
      const updateData: any = {
        status: status || 'COMPLETED',
        completed_at: completed_at || new Date().toISOString()
      };
      
      // If generated image URL is provided, update it as well
      if (generatedImageUrl) {
        updateData.generated_image_url = generatedImageUrl;
      }
      
      // If user data is available, include in the update
      if (userData) {
        // 优先使用新获取的用户数据
        updateData.nickname = userData.nickname;
        updateData.email = userData.email;
      } else if (existingRecord.nickname || existingRecord.email) {
        // 如果没有新数据但记录中已有数据，则保留现有数据
        updateData.nickname = existingRecord.nickname;
        updateData.email = existingRecord.email;
      }
      
      console.log('Update data:', {
        ...updateData,
        generatedImageUrl: updateData.generated_image_url ? updateData.generated_image_url.substring(0, 30) + '...' : null
      });
      
      const { data, error } = await supabase
        .from("4o_generations")
        .update(updateData)
        .eq("task_id", currentTaskId)
        .eq("user_uuid", userUuid)
        .select();
        
      if (error) {
        console.error(`Error updating record with task ID [${currentTaskId}]:`, error);
        console.error("Error details:", {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        continue;
      }
      
      console.log(`Successfully updated record for task ID [${currentTaskId}]:`, data);
      updateSuccess = true;
      updateResult = data;
      break;
    }
    
    if (!updateSuccess) {
      return NextResponse.json(
        { error: "No matching record found or update failed", triedTaskIds: taskIdVariations },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: "Record status updated successfully",
      result: updateResult
    });
    
  } catch (error) {
    console.error("Error processing update status request:", error);
    return NextResponse.json(
      { error: "Internal server error", message: (error as Error).message },
      { status: 500 }
    );
  }
} 