:root {
  /* 老照片修复主题 - 浅色模式 */
  --background: 210 50% 98%;  /* 非常浅的蓝色背景 #f5f9fc */
  --foreground: 215 25% 27%;  /* 深蓝灰色文本 #3a4a5c */
  --card: 210 50% 98%;
  --card-foreground: 215 25% 27%;
  --popover: 210 50% 98%;
  --popover-foreground: 215 25% 27%;
  --primary: 210 100% 50%;    /* 蓝色主色调 #0066ff */
  --primary-foreground: 0 0% 100%;
  --secondary: 195 63% 79%;   /* 天蓝色 #a8d8ea */
  --secondary-foreground: 195 80% 20%;
  --muted: 210 40% 93%;
  --muted-foreground: 215 15% 50%;
  --accent: 210 80% 65%;      /* 亮蓝色强调 #66a3ff */
  --accent-foreground: 210 80% 20%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 210 30% 85%;     /* 浅蓝色边框 */
  --input: 210 30% 85%;
  --ring: 210 100% 50%;
  --radius: 0.75rem;        /* 圆角半径 */
  
  /* 图表颜色 */
  --chart-1: 210 100% 50%;   /* 蓝色 */
  --chart-2: 195 63% 79%;    /* 天蓝色 */
  --chart-3: 210 80% 65%;    /* 亮蓝色 */
  --chart-4: 225 60% 70%;    /* 紫蓝色 #8c9cff */
  --chart-5: 240 50% 80%;    /* 淡紫色 #b3b3ff */
  
  /* 老照片修复特定颜色 */
  --color-sky: 195 63% 79%;      /* 天蓝色 #a8d8ea */
  --color-nature: 210 100% 50%;  /* 蓝色 #0066ff */
  --color-earth: 210 40% 93%;    /* 浅蓝灰色 #e6f0f9 */
  --color-magic: 225 60% 70%;    /* 紫蓝色 #8c9cff */
  --color-sunset: 210 80% 65%;   /* 亮蓝色 #66a3ff */
  --color-emotion: 240 50% 80%;  /* 淡紫色 #b3b3ff */
  
  /* 老照片修复背景色 */
  --photo-restore-gradient-from: #e6f2ff; /* 浅蓝色 */
  --photo-restore-gradient-to: #ffffff; /* 白色 */
}

.dark {
  /* 老照片修复主题 - 深色模式 */
  --background: 215 30% 12%;   /* 深蓝灰色背景 #141e2c */
  --foreground: 210 25% 90%;   /* 浅蓝灰色文本 */
  --card: 215 30% 12%;
  --card-foreground: 210 25% 90%;
  --popover: 215 30% 12%;
  --popover-foreground: 210 25% 90%;
  --primary: 210 80% 50%;     /* 蓝色 #0066cc */
  --primary-foreground: 210 15% 95%;
  --secondary: 210 48% 44%;   /* 深蓝色 #3a6ea5 */
  --secondary-foreground: 195 40% 90%;
  --muted: 215 30% 18%;
  --muted-foreground: 210 15% 70%;
  --accent: 210 70% 45%;      /* 深蓝色强调 #0052cc */
  --accent-foreground: 210 40% 90%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 215 25% 25%;      /* 深蓝色边框 */
  --input: 215 25% 25%;
  --ring: 210 80% 50%;
  
  /* 深色模式图表颜色 */
  --chart-1: 210 80% 50%;    /* 蓝色 */
  --chart-2: 210 48% 44%;    /* 深蓝色 */
  --chart-3: 210 70% 45%;    /* 深蓝色强调 */
  --chart-4: 225 40% 50%;    /* 深紫蓝色 #4d5aa3 */
  --chart-5: 240 30% 60%;    /* 深紫色 #6666b3 */
  
  /* 老照片修复特定颜色 - 深色模式 */
  --color-sky: 210 48% 44%;    /* 深蓝色 #3a6ea5 */
  --color-nature: 210 80% 50%; /* 蓝色 #0066cc */
  --color-earth: 215 15% 35%;  /* 深蓝灰色 #3d4c5c */
  --color-magic: 225 40% 50%;  /* 深紫蓝色 #4d5aa3 */
  --color-sunset: 210 70% 45%; /* 深蓝色强调 #0052cc */
  --color-emotion: 240 30% 60%;/* 深紫色 #6666b3 */
  
  /* 深色模式下的老照片修复背景色 */
  --photo-restore-gradient-from: #1a3a5f; /* 深蓝色 */
  --photo-restore-gradient-to: #2c3e50; /* 深灰蓝色 */
}