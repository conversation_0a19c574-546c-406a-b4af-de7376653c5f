"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Upload, X } from "lucide-react";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { v4 as uuidv4 } from 'uuid';

export const Generator4o = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [aspectRatio, setAspectRatio] = useState<string>("1:1");
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [result, setResult] = useState<{
    generatedImageUrl: string | null;
    originalImageUrl: string | null;
  }>({ 
    generatedImageUrl: null,
    originalImageUrl: null
  });
  const [error, setError] = useState<string | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [userCredits, setUserCredits] = useState<number | null>(null);
  const [perceptionProgress, setPerceptionProgress] = useState<number>(0);
  const [mounted, setMounted] = useState(false);
  const [requiredCredits, setRequiredCredits] = useState<number>(3); // Default credits required

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (session) {
      fetch('/api/credits')
        .then(res => res.json())
        .then(data => {
          setUserCredits(data.left_credits);
        })
        .catch(error => {
          console.error('Error fetching credits:', error);
        });
    }
  }, [session]);

  useEffect(() => {
    if (result.generatedImageUrl && session) {
      console.log("Image generated successfully, refreshing credits immediately");
      fetch('/api/credits')
        .then(res => res.json())
        .then(data => {
          setUserCredits(data.left_credits);
        })
        .catch(error => {
          console.error('Error refreshing credits after generation:', error);
        });
    }
  }, [result.generatedImageUrl, session]);

  // 感知进度模拟
  useEffect(() => {
    if (isProcessing && status === "GENERATING" && progress < 10) {
      // Start with immediate feedback
      setPerceptionProgress(prev => Math.max(prev, 5)); // 使用Math.max避免降低现有进度
      
      // Create a simulated progress that moves forward even when real progress is stuck at 0%
      const simulatedInterval = setInterval(() => {
        setPerceptionProgress(prev => {
          // Cap the simulated progress at 95% until we get real confirmation
          if (prev < 30) return prev + 1;
          if (prev < 60) return prev + 0.5;
          if (prev < 80) return prev + 0.2;
          if (prev < 90) return prev + 0.1;
          return prev;
        });
      }, 800);
      
      return () => clearInterval(simulatedInterval);
    }
  }, [isProcessing, status, progress]);

  // When we get real progress updates, sync with perception progress if higher
  useEffect(() => {
    if (progress > perceptionProgress) {
      setPerceptionProgress(progress);
    }
  }, [progress]);

  // 状态重置函数
  const resetProcessingState = (errorMessage?: string) => {
    setIsProcessing(false);
    setStatus(null);
    setProgress(0);
    setPerceptionProgress(0);
    if (errorMessage) {
      setError(errorMessage);
    }
  };

  // 轮询任务状态
  useEffect(() => {
    if (taskId) {
      // Add a timeout to stop polling after a reasonable time (10 minutes)
      let pollingTimeout: NodeJS.Timeout | null = null;
      
      const stopPolling = (interval: NodeJS.Timeout) => {
        clearInterval(interval);
        if (pollingTimeout) clearTimeout(pollingTimeout);
        
        // If still processing with no result after timeout, show an error
        if (isProcessing && !error) {
          resetProcessingState("Processing timed out. Please try again or use a different image.");
          toast.error("Processing timed out");
        }
      };
      
      // Set timeout to stop polling after 10 minutes
      pollingTimeout = setTimeout(() => {
        console.log("Polling timeout reached after 10 minutes");
        if (interval) stopPolling(interval);
      }, 10 * 60 * 1000);

      // 检查任务是否已经失败的更短间隔
      const failureCheckInterval = setInterval(async () => {
        try {
          const timestamp = Date.now();
          const response = await fetch(`/api/create4o/record-info/${taskId}?_t=${timestamp}`, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          
          if (!response.ok) {
            return;
          }
          
          const data = await response.json();
          
          const status = data.data?.status;
          const successFlag = data.data?.successFlag;
          const errorCode = data.data?.errorCode;
          const errorMessage = data.data?.errorMessage;
          
          // 如果检测到失败状态，立即处理
          if (successFlag === 3 || status === "GENERATE_FAILED") {
            clearInterval(failureCheckInterval);
            clearInterval(interval);
            if (pollingTimeout) clearTimeout(pollingTimeout);
            
            // 设置具体的错误消息
            if (errorCode === 400) {
              if (errorMessage?.includes("violating content policies")) {
                resetProcessingState("Your content was flagged as violating content policies. Please try uploading a different image.");
                toast.error("Content policy violation. Please try a different image.");
              } else if (errorMessage?.includes("The OpenAI did not return the image")) {
                resetProcessingState("There are too many users at the moment. Please try again later.");
                toast.error("Too many users. Please try again later.");
              }
            } else if (errorCode === 500 && errorMessage?.includes("Internal Error")) {
              resetProcessingState("We encountered a network error. Please try again later.");
              toast.error("Network error. Please try again later.");
            } else {
              resetProcessingState("This image cannot be processed. Please try uploading a different image.");
              toast.error("Please try uploading a different image");
            }
          }
        } catch (error) {
          console.error("Error in quick failure check:", error);
        }
      }, 1000); // 每1秒检查一次失败状态

      const interval = setInterval(async () => {
        try {
          const timestamp = Date.now();
          console.log(`Polling task status for taskId: ${taskId} at ${new Date().toISOString()}`);
          const response = await fetch(`/api/create4o/record-info/${taskId}?_t=${timestamp}`, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          const data = await response.json();
          
          if (!response.ok) {
            throw new Error(data.error || "Failed to fetch task status");
          }

          // 更新状态
          const status = data.data?.status;
          const successFlag = data.data?.successFlag;
          const currentProgress = data.data?.progress;
          const errorCode = data.data?.errorCode;
          const errorMessage = data.data?.errorMessage;
          
          setStatus(status);
          
          // 确保进度只增不减
          const newProgress = Math.round(parseFloat(currentProgress || "0") * 100);
          if (newProgress > progress) {
            setProgress(newProgress);
          }
          console.log(`Current status: ${status}, successFlag: ${successFlag}, progress: ${currentProgress}`);

          // Check for failure conditions first
          if (successFlag === 3 || status === "GENERATE_FAILED") {
            // 清除轮询和超时
            clearInterval(interval);
            clearInterval(failureCheckInterval);
            if (pollingTimeout) clearTimeout(pollingTimeout);
            
            resetProcessingState();
            
            // 设置错误消息
            if (errorCode === 400) {
              if (errorMessage?.includes("violating content policies")) {
                setError("Your content was flagged as violating content policies. Please try uploading a different image.");
                toast.error("Content policy violation. Please try a different image.");
              } else if (errorMessage?.includes("The OpenAI did not return the image")) {
                setError("There are too many users at the moment. Please try again later.");
                toast.error("Too many users. Please try again later.");
              }
            } else if (errorCode === 500 && errorMessage?.includes("Internal Error")) {
              setError("We encountered a network error. Please try again later.");
              toast.error("Network error. Please try again later.");
            } else {
              setError("This image cannot be processed. Please try uploading a different image.");
              toast.error("Please try uploading a different image");
            }
            
            return;
          }

          // 检查进度和状态
          if (currentProgress === "1.00" || (status === "SUCCESS" && successFlag === 1)) {
            const resultUrls = data.data?.response?.resultUrls;
            if (resultUrls && resultUrls.length > 0) {
              console.log("Success! Result URL:", resultUrls[0]);
              setResult({
                generatedImageUrl: resultUrls[0],
                originalImageUrl: resultUrls[0]
              });
              setIsProcessing(false);
              // 确保清除所有轮询
              clearInterval(interval);
              clearInterval(failureCheckInterval);
              if (pollingTimeout) clearTimeout(pollingTimeout);
              setTaskId(null); // 重置taskId以防止轮询重新触发
              setStatus("SUCCESS"); // 明确设置成功状态
              setProgress(100);
              setPerceptionProgress(100);
              toast.success("Image generated successfully!");
              
              // Now that we have a successful generation, deduct credits
              console.log("Image generation successful, deducting credits:", requiredCredits);
              await deductCredits();
              
              const imageUrl = resultUrls[0];
              if (imageUrl) {
                const uniqueFilename = `ghibli-${uuidv4()}.png`;
                uploadToR2(imageUrl, uniqueFilename);
              }
            }
          } else if (status === "GENERATING") {
            console.log(`Generating... Progress: ${progress}%`);
          }
        } catch (error) {
          console.error("Error polling task status:", error);
        }
      }, 3000);
      
      return () => {
        if (interval) {
          clearInterval(interval);
        }
        if (failureCheckInterval) {
          clearInterval(failureCheckInterval);
        }
        if (pollingTimeout) {
          clearTimeout(pollingTimeout);
        }
      };
    }
  }, [taskId, isProcessing, error, progress]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        setError('Please upload an image file');
        return;
      }

      // Clear existing state
      setError(null);
      setIsProcessing(false);
      setTaskId(null);
      setProgress(0);

      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        if (typeof result === 'string' && result.startsWith('data:image/')) {
          setSelectedImage(result);
          setResult({ generatedImageUrl: null, originalImageUrl: null });
          setError(null);
        } else {
          setError('Invalid image format');
        }
      };
      reader.onerror = () => {
        setError('Failed to read image file');
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerate = async () => {
    if (!selectedImage) {
      setError("Please upload an image first");
      return;
    }

    if (!session) {
      toast.error("Please sign in to use this feature");
      router.push("/auth/signin");
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);
      setTaskId(null);
      setStatus(null);
      setResult({ generatedImageUrl: null, originalImageUrl: null });
      setProgress(0);
      setPerceptionProgress(0);
      
      const response = await fetch("/api/create4o", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          image: selectedImage,
          prompt: "Create studio ghibli style photos",
          size: aspectRatio
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.error === "insufficient_credits") {
          toast.error("Insufficient credits. Please recharge.");
          router.push("/pricing");
          return;
        }
        throw new Error(errorData.error || "Failed to process image");
      }

      const data = await response.json();
      setTaskId(data.taskId);
      // Store the required credits for later deduction
      if (data.requiredCredits) {
        setRequiredCredits(data.requiredCredits);
      }
      setStatus("GENERATING");
      setPerceptionProgress(5);
      toast.success("Image generation task submitted successfully! Processing may take a few minutes.");
    } catch (error) {
      console.error("Error:", error);
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(`Failed to process image: ${errorMessage}`);
      setIsProcessing(false);
      toast.error("Failed to process image");
    }
  };

  const handleClearImage = () => {
    setSelectedImage(null);
    setResult({ generatedImageUrl: null, originalImageUrl: null });
    resetProcessingState();
  };

  const uploadToR2 = async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceUrl: imageUrl,
          filename: filename,
          taskId: taskId
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }
      
      setResult(prevResult => ({
        generatedImageUrl: data.url,
        originalImageUrl: prevResult.originalImageUrl
      }));
      
      console.log("R2 upload URL:", data.url);
      
    } catch (err: any) {
      console.error("R2 upload failed:", err);
    }
  };

  // Add deductCredits function
  const deductCredits = async () => {
    try {
      const response = await fetch('/api/deduct-credits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          credits: requiredCredits
        }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setUserCredits(data.creditsLeft);
        console.log("Credits deducted successfully after generation");
      } else {
        console.error("Failed to deduct credits:", data.error);
      }
    } catch (error) {
      console.error("Error deducting credits:", error);
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto p-6 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Ghibli Ai Image Generator</h2>
          <div className="bg-purple-100 text-purple-600 px-3 py-1 rounded-full flex items-center gap-2">
            <span className="text-sm font-normal">Credits:</span>{" "}
            {mounted && session ? (
              <span className="text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
            ) : (
              <span className="text-lg font-bold">
                <a href="/auth/signin" className="hover:underline">login</a>
              </span>
            )}
            <svg
              className="w-4 h-4 text-amber-500 fill-current"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
            >
              <path d="M12 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0-15c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h12c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9zm0 3c3.31 0 6 2.69 6 6v7c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-7c0-3.31 2.69-6 6-6z" />
            </svg>
          </div>
        </div>

        {/* Add navigation to login for non-logged in users */}
        {mounted && !session && (
          <div className="text-center">
            <a 
              href="/auth/signin" 
              className="text-purple-600 hover:text-purple-800 underline text-sm"
            >
              <strong>Log in to Free Trial</strong>
            </a>
          </div>
        )}
        <p className="text-sm text-muted-foreground text-center">Step 1: Click <strong>Sign in</strong> to Free Trial</p>
        <p className="text-sm text-muted-foreground text-center">Step 2: Upload an image to transform it into Studio Ghibli style</p>
        
        <div className="relative min-h-[300px] rounded-lg border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
          <input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
            aria-label="Upload image"
          />
          <div className="absolute inset-0 flex flex-col items-center justify-center gap-2">
            {selectedImage ? (
              <div className="relative w-full h-full">
                <Image
                  src={selectedImage}
                  alt="Uploaded image"
                  className="object-contain"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority
                />
                <Button
                  variant="secondary"
                  size="icon"
                  className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm hover:bg-background/90 z-20"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleClearImage();
                  }}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Clear image</span>
                </Button>
              </div>
            ) : (
              <div className="flex flex-col items-center gap-2 text-muted-foreground hover:text-muted-foreground/80 transition-colors">
                <Upload className="w-8 h-8" />
                <p className="text-sm font-medium">Upload Image</p>
                <p className="text-xs text-muted-foreground/70">Click or drag and drop</p>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Select Aspect Ratio
          </label>
          <Select
            value={aspectRatio}
            onValueChange={setAspectRatio}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select aspect ratio" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1:1">Square (1:1)</SelectItem>
              <SelectItem value="3:2">Landscape (3:2)</SelectItem>
              <SelectItem value="2:3">Portrait (2:3)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <Button 
          className="w-full bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-700 hover:to-purple-900 text-white"
          size="lg"
          onClick={handleGenerate}
          disabled={!selectedImage || isProcessing}
        >
          <span className="flex items-center gap-2">
            {isProcessing ? (
              <>
                <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                {status === "GENERATING" ? (
                  <>
                    Generating... <span className="numeric-content">{Math.round(perceptionProgress)}%</span>
                  </>
                ) : "Processing..."}
              </>
            ) : (
              <>
                <svg
                  className="w-5 h-5"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 6V18M18 12L6 12"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Generate Ghibli-style Now
              </>
            )}
          </span>
        </Button>
        
        {status === "GENERATING" && (
          <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
            <div 
              className="bg-purple-600 h-2.5 rounded-full transition-all duration-300" 
              style={{ width: `${perceptionProgress}%` }}
            ></div>
          </div>
        )}

        {result.generatedImageUrl && (
          <div className="space-y-4">
            <Button 
              className="w-full bg-green-600 hover:bg-green-700 text-white"
              size="lg"
              onClick={() => {
                if (result.generatedImageUrl) {
                  window.open(result.generatedImageUrl, '_blank');
                }
              }}
            >
              <span className="flex items-center gap-2">
                <svg 
                  className="w-5 h-5" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
                Open This Image
              </span>
            </Button>
            
            <Button 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              size="lg"
              onClick={async () => {
                try {
                  const urlToDownload = result.originalImageUrl || result.generatedImageUrl;
                  if (!urlToDownload) {
                    throw new Error('No image URL available');
                  }
                  
                  const response = await fetch(urlToDownload);
                  const blob = await response.blob();
                  
                  const url = window.URL.createObjectURL(blob);
                  
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = 'ghibli-ai-image-generator.com.png';
                  document.body.appendChild(link);
                  link.click();
                  
                  document.body.removeChild(link);
                  window.URL.revokeObjectURL(url);
                } catch (error) {
                  console.error('Error downloading image:', error);
                  toast.error('Failed to download image');
                }
              }}
            >
              <span className="flex items-center gap-2">
                <svg 
                  className="w-5 h-5" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                  />
                </svg>
                Download Image
              </span>
            </Button>
            
            <div className="rounded-lg overflow-hidden">
              <div 
                className="relative aspect-square w-full cursor-pointer" 
                onClick={() => {
                  if (result.generatedImageUrl) {
                    window.open(result.generatedImageUrl, '_blank');
                  }
                }}
              >
                <Image
                  src={result.generatedImageUrl}
                  alt="Generated image"
                  className="object-contain"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            </div>
          </div>
        )}

        {status === "GENERATING" && (
          <div className="p-4 rounded-lg bg-amber-100/20 border border-amber-200">
            <p className="text-sm text-amber-800">
              Your image is being generated. This typically takes 1-2 minutes. Please don't close this page.
            </p>
          </div>
        )}

        {error && error.includes("insufficient_credits") ? (
          <div className="p-4 rounded-lg bg-destructive/10">
            <p className="text-sm text-foreground/90">
              Insufficient credits. Please recharge to continue generating images.
            </p>
            <Button
              className="mt-2 w-full"
              variant="outline"
              onClick={() => router.push("/pricing")}
            >
              Recharge Credits
            </Button>
          </div>
        ) : error && (
          <div className="p-4 rounded-lg bg-destructive/10">
            <p className="text-sm text-foreground/90">{error}</p>
          </div>
        )}
      </div>
    </Card>
  );
}; 