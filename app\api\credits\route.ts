import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";

export const runtime = "edge";

export async function GET() {
  try {
    // Get user UUID using the same method as my-credits page
    const user_uuid = await getUserUuid();
    
    if (!user_uuid) {
      return NextResponse.json(
        { error: "User not authenticated" },
        { status: 401 }
      );
    }
    
    // Get user credits using the same method as my-credits page
    const userCredits = await getUserCredits(user_uuid);
    
    // Return user credits in the format expected by the client
    return NextResponse.json(userCredits);
  } catch (error) {
    console.error("Error fetching user credits:", error);
    return NextResponse.json(
      { error: "Failed to fetch user credits" },
      { status: 500 }
    );
  }
} 