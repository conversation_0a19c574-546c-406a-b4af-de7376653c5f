"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { ImageR2Uploader } from "@/components/ui/ImageR2Uploader";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

export default function R2UploadButton() {
  const [showUploader, setShowUploader] = useState(false);

  return (
    <div className="flex justify-center mt-4">
      <Button 
        onClick={() => setShowUploader(true)}
        className="flex items-center gap-2"
        variant="secondary"
      >
        <Upload className="h-4 w-4" />
        Upload Specific Image to R2
      </Button>

      <Dialog open={showUploader} onOpenChange={setShowUploader}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Upload Image to R2</DialogTitle>
          </DialogHeader>
          <ImageR2Uploader 
            sourceUrl="https://tempfile.aiquickdraw.com/v/c83fce51-56f5-4475-8f84-6efe4d5501bf_0.png"
            filename="c83fce51-56f5-4475-8f84-6efe4d5501bf_0.png"
            onUploadComplete={() => {
              // Auto-close the dialog after successful upload (optional)
              setTimeout(() => setShowUploader(false), 3000);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
} 