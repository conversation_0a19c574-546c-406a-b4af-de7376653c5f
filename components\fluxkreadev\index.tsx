"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import Image from "next/image";
import { v4 as uuidv4 } from "uuid";

interface FluxKreaDevProps {
  title?: string;
  subtitle?: string;
}

export default function FluxKreaDev({
  title = "Flux Krea Dev AI Image Generator",
  subtitle = "Select a style, type to get your own flux ai image"
}: FluxKreaDevProps = {}) {
  // Session and mounting state
  const { data: session } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [userCredits, setUserCredits] = useState<number | null>(null);

  // Form states
  const [prompt, setPrompt] = useState("Three adults are dressed in colorful animal costumes at a club, including a blue bunny with a white cloud pattern, a pink bunny with white polka dots, and a yellow bunny with pink ears, all smiling and posing for a photo");
  const [aspectRatio, setAspectRatio] = useState("1:1");
  const [seed, setSeed] = useState("");
  const [watermark, setWatermark] = useState(true);
  const [displayPublic, setDisplayPublic] = useState(true);
  const [imageCount, setImageCount] = useState(1);

  // Generation states
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  useEffect(() => {
    setMounted(true);
    
    // Fetch user credits if logged in
    if (session?.user?.email) {
      fetchUserCredits();
    }
  }, [session]);

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (response.ok) {
        const data = await response.json();
        setUserCredits(data.credits);
      }
    } catch (error) {
      console.error('Error fetching user credits:', error);
    }
  };

  const handleGenerate = async () => {
    if (!session) {
      toast.error('Please sign in to generate images');
      router.push('/auth/signin');
      return;
    }

    if (!prompt.trim()) {
      toast.error('Please enter a prompt');
      return;
    }

    // Check if user has enough credits
    if (userCredits !== null && userCredits < 3) {
      toast.error('Insufficient credits. Each image generation requires 3 credits');
      router.push('/pricing');
      return;
    }

    setIsGenerating(true);
    toast.info('Starting image generation...', { duration: 3000 });

    try {
      // Generate unique task ID
      const taskId = `fluxkrea_${uuidv4()}`;
      
      const response = await fetch('/api/generate/flux-krea', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          aspect_ratio: aspectRatio,
          seed: seed || undefined,
          watermark,
          display_public: displayPublic,
          image_count: imageCount,
          task_id: taskId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Generation failed');
      }

      const data = await response.json();

      if (data.images && data.images.length > 0) {
        setGeneratedImages(data.images);
        setActiveImageIndex(0);
        toast.success('Image generated successfully!');

        // Refresh user credits
        await fetchUserCredits();
      } else {
        throw new Error('No images generated');
      }
    } catch (error) {
      console.error('Generation error:', error);
      toast.error(error instanceof Error ? error.message : 'Generation failed');
    } finally {
      setIsGenerating(false);
    }
  };

  const aspectRatioOptions = [
    { value: "16:9", label: "16:9", width: "16", height: "9" },
    { value: "3:2", label: "3:2", width: "3", height: "2" },
    { value: "4:3", label: "4:3", width: "4", height: "3" },
    { value: "1:1", label: "1:1", width: "1", height: "1" },
    { value: "4:5", label: "4:5", width: "4", height: "5" }
  ];

  const imageCountOptions = [1, 2, 3, 4, 5];

  return (
    <section className="py-12 bg-background">
      <div className="w-full max-w-6xl mx-auto p-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-2">{title}</h2>
          <p className="text-lg text-muted-foreground">{subtitle}</p>
        </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left panel */}
        <Card className="p-6 space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold">Settings</h3>
            <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full flex items-center gap-2">
              <span className="text-sm font-normal">Credits:</span>{" "}
              {mounted && session ? (
                <span className="text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
              ) : (
                <span className="text-lg font-bold">
                  <a href="/auth/signin" className="hover:underline">Sign in</a>
                </span>
              )}
            </div>
          </div>

          {/* Prompt */}
          <div className="space-y-2">
            <Label htmlFor="prompt" className="text-sm font-medium">Prompt</Label>
            <Textarea
              id="prompt"
              placeholder="Describe the image you want to generate..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[120px] resize-none text-sm"
            />
          </div>

          {/* Image Dimensions */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Image Dimensions</Label>
            <div className="grid grid-cols-5 gap-2">
              {aspectRatioOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={aspectRatio === option.value ? "default" : "outline"}
                  className="h-14 aspect-square flex flex-col items-center justify-center p-1 text-xs"
                  onClick={() => setAspectRatio(option.value)}
                >
                  <div className="text-center">
                    <span className="block font-medium">{option.width}</span>
                    <span className="block font-medium">{option.height}</span>
                  </div>
                </Button>
              ))}
            </div>
          </div>

          {/* Seed */}
          <div className="space-y-2">
            <Label htmlFor="seed" className="text-sm">Seed (Optional)</Label>
            <Input
              id="seed"
              type="number"
              placeholder="Random seed for reproducible results"
              value={seed}
              onChange={(e) => setSeed(e.target.value)}
              className="text-sm"
            />
          </div>

          {/* Watermark */}
          <div className="flex items-center justify-between">
            <Label htmlFor="watermark" className="text-sm">Watermark</Label>
            <Switch
              id="watermark"
              checked={watermark}
              onCheckedChange={setWatermark}
            />
          </div>

          {/* Display Public */}
          <div className="flex items-center justify-between">
            <Label htmlFor="display-public" className="text-sm">Display Public</Label>
            <Switch
              id="display-public"
              checked={displayPublic}
              onCheckedChange={setDisplayPublic}
            />
          </div>

          {/* Number of Images */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Number of Images</Label>
            <p className="text-xs text-muted-foreground">Select how many images to generate</p>
            <div className="flex gap-2">
              {imageCountOptions.map((count) => (
                <Button
                  key={count}
                  variant={imageCount === count ? "default" : "outline"}
                  size="sm"
                  className="w-8 h-8 text-xs"
                  onClick={() => setImageCount(count)}
                >
                  {count}
                </Button>
              ))}
            </div>
          </div>

          {/* Generate Button */}
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.trim()}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium"
            size="lg"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating...
              </>
            ) : (
              <>
                <span className="mr-2">🎨</span>
                Generate Image {imageCount > 1 ? `(${imageCount})` : ''} 3 Credit 💎
              </>
            )}
          </Button>

          {/* Contact */}
          <div className="text-center text-sm text-muted-foreground">
            <span>Contact: </span>
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </div>
        </Card>

        {/* Right panel */}
        <Card className="p-6 space-y-4">
          <h3 className="text-xl font-semibold">Generated Image</h3>
          
          <div className="relative aspect-[4/3] w-full bg-muted rounded-lg overflow-hidden">
            {generatedImages.length > 0 ? (
              <Image
                src={generatedImages[activeImageIndex]}
                alt="Generated image"
                fill
                className="object-contain"
                priority
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <p className="text-muted-foreground">
                  {isGenerating ? 'Generating...' : 'Generated image will appear here'}
                </p>
              </div>
            )}
          </div>
          
          {/* Image navigation buttons */}
          {generatedImages.length > 1 && (
            <div className="flex justify-center gap-2">
              {generatedImages.map((_, index) => (
                <Button 
                  key={index}
                  variant={activeImageIndex === index ? "default" : "outline"}
                  size="icon"
                  className="w-8 h-8"
                  onClick={() => setActiveImageIndex(index)}
                >
                  {index + 1}
                </Button>
              ))}
            </div>
          )}

          {generatedImages.length > 0 && (
            <Button 
              className="w-full" 
              size="lg"
              onClick={() => {
                const link = document.createElement('a');
                link.href = generatedImages[activeImageIndex];
                link.download = `flux-krea-dev-${Date.now()}.png`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              Download Image
            </Button>
          )}
        </Card>
      </div>
      </div>
    </section>
  );
}
