import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import HeroWithCompare from "@/components/blocks/hero-with-compare";
import ImageCompareGallery from "@/components/blocks/image-compare-gallery";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import { Generator4o } from "@/components/generator4o";
import { Kontext } from "@/components/kontext";
import KontextDev from "@/components/kontextdev";
import FluxKreaDev from "@/components/fluxkreadev";
import { Button } from "@/components/ui/button";
import Link from "next/link";


export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  // These can be updated with the URLs provided by the user
  const compareImages = {
    originalSrc: "https://pic.kontext-dev.com/woman-with-snowflakes-on-her-face.webp",
    modifiedSrc: "https://pic.kontext-dev.com/woman-with-snowflakes-on-her-face-kontext-dev-edited.webp",
    beforeText: "Original portrait",
    afterText: "\"Remove the snowflakes from her face\""
  };

  // 示例图片对比数据 - 这些应该替换为实际的图片URL
  const compareGroups = [
    {
      id: 1,
      originalSrc: "https://pic.kontext-dev.com/elderly-man-wearing-hat.webp",
      modifiedSrc: "https://pic.kontext-dev.com/elderly-man-wearing-hat-standing-by-the-sea.webp",
      alt: "An elderly man wearing a hat with a modified background, edited using Kontext Dev",
      beforeText: "Original photo",
      afterText: "Change the background"
    },
    {
      id: 2,
      originalSrc: "https://pic.kontext-dev.com/boy-holding-a-stack-of-books-original.webp",
      modifiedSrc: "https://pic.kontext-dev.com/boy-holding-a-stack-of-books-pencil-sketch.webp",
      alt: "Convert the photo to pencil sketch with Kontext Dev",
      beforeText: "Original photo",
      afterText: "Convert to pencil sketch"
    },
    {
      id: 3,
      originalSrc: "https://pic.kontext-dev.com/choose-joy.webp",
      modifiedSrc: "https://pic.kontext-dev.com/choose-dev.webp",
      alt: "Replace 'Choose joy' with 'Choose Dev'",
      beforeText: "Original photo",
      afterText: "Replace Text"
    },
    {
      id: 4,
      originalSrc: "https://pic.kontext-dev.com/blue-car-pink-house-street.webp",
      modifiedSrc: "https://pic.kontext-dev.com/pink-house-street-without-car.webp",
      alt: "Remove the car from the image",
      beforeText: "Original photo",
      afterText: "Remove the Car"
    },
    {
      id: 5,
      originalSrc: "https://pic.restore-old-photos.com/family-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/family-photo-ai-restored.jpg",
      alt: "Restore Family Damaged Old Photos",
      beforeText: "Group photo with damage",
      afterText: "Restore old Photo"
    },
    {
      id: 6,
      originalSrc: "https://pic.kontext-dev.com/color-page.webp",
      modifiedSrc: "https://pic.kontext-dev.com/colorized-page-with-kontext-dev.webp",
      alt: "Colorize the page",
      beforeText: "Original photo",
      afterText: "Colorized with Kontext dev"
    }
  ];

  return (
    <>
      {page.hero && <HeroWithCompare
        hero={page.hero}
        compareImages={compareImages}
      />}
      <FluxKreaDev />
      <KontextDev />
      
      
      
      {/* 图片对比画廊 */}
      <ImageCompareGallery 
        title="Kontext Dev - Advanced Flux Kontext Image Editing"
        description="See how Kontext Dev transforms images with multi-modal technology. From character consistency across scenes to precise local editing and style transfer, these examples showcase the power of Flux context in creative workflows."
        compareGroups={compareGroups} 
      />

      {page.branding && <Branding section={page.branding} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      
      
      
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      
      {/* Pricing Section - Just a button to pricing page */}
      <div className="container py-12 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Ready to Get Started?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Explore our pricing plans and find the perfect option for your creative needs
          </p>
          <Link href="/pricing">
            <Button size="lg" className="gap-2">
              View Pricing Plans
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>
      
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
      
    </>
  );
}
