import { getSupabaseClient } from "./db";

export interface Generation {
  id?: number;
  user_uuid: string;
  nickname?: string;
  email?: string;
  image_created_url: string;
  image_created_at: string;
  created_at: string;
}

export interface Generation4o {
  id?: number;
  user_uuid: string;
  nickname?: string;
  email?: string;
  original_image_url: string;
  generated_image_url: string;
  prompt: string;
  status: string;
  created_at: string;
  completed_at?: string;
  task_id: string;
  callback_id: string;
}

/**
 * 获取用户的图片生成记录
 */
export async function getGenerationsByUserUuid(
  user_uuid: string,
  page: number = 1,
  limit: number = 20
): Promise<Generation[] | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("generations")
    .select("*")
    .eq("user_uuid", user_uuid)
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    console.error("Error fetching generations:", error);
    return undefined;
  }

  return data;
}

/**
 * 获取用户的4o图片生成记录
 */
export async function get4oGenerationsByUserUuid(
  user_uuid: string,
  page: number = 1,
  limit: number = 20
): Promise<Generation4o[] | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("4o_generations")
    .select("*")
    .eq("user_uuid", user_uuid)
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    console.error("Error fetching 4o generations:", error);
    return undefined;
  }

  return data;
}