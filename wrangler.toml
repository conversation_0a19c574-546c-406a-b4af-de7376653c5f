name = "kontext-dev"
compatibility_date = "2024-07-29"
compatibility_flags = ["nodejs_compat"]
pages_build_output_dir = ".vercel/output/static"

[vars]
# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "https://kontext-dev.com"
NEXT_PUBLIC_PROJECT_NAME = "kontext-dev.com"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
SUPABASE_URL = "https://supabase.nancook.com"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NDk3Nzc0NTcsImV4cCI6MTg5MzQ1NjAwMCwiaXNzIjoiZG9rcGxveSJ9.LPewOYrTooydDgoBI5kBlkRvcX0JaLg41Z4soMty8z4"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NDk3Nzc0NTcsImV4cCI6MTg5MzQ1NjAwMCwiaXNzIjoiZG9rcGxveSJ9.LPewOYrTooydDgoBI5kBlkRvcX0JaLg41Z4soMty8z4"

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = "************-mesf9obg38k1ks2ht9h7s3pns5vpe5im.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "************-mesf9obg38k1ks2ht9h7s3pns5vpe5im.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "true"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = "G-8TGT7NN1C1"

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = "pk_live_51Rh7YBAKh1PRJ7q9ZBpnGNLvZhHEpSntOCQ9R7iAGk1j60e55kNLS3VAKD90MCrzYSuw4Hx5ihggoEgUlvtbvGkB00hQgIR2YT"
STRIPE_PRIVATE_KEY = "***********************************************************************************************************"
STRIPE_WEBHOOK_SECRET = "whsec_CeMhGU5ikEsEFAdMBjI3BXmlvhuhJLc1"

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = "<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk，R2
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = "https://b8617f91531e25bf5bfb036b5cca1ada.r2.cloudflarestorage.com"
STORAGE_REGION = "auto"
STORAGE_ACCESS_KEY = "afa9c9e31ce9a45015f5db38b04b8c19"
STORAGE_SECRET_KEY = "aeef76587eecba288435b3389ed3f518b5d210348f6967c5dab0a46e5f8b3e33"
STORAGE_BUCKET = "kontextdev"
STORAGE_DOMAIN = "https://img.kontext-dev.com"

REPLICATE_API_TOKEN = "****************************************"

# Kie.ai API配置
KIE_API_KEY="246f3ffbf0e6cb0023ae921699963ab6"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
DEEPSEEK_API_KEY="***********************************"





