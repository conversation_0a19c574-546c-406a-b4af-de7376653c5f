import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getSupabaseClient } from "@/models/db";
import { v4 as uuidv4 } from "uuid";

export const runtime = 'edge';

// Kie.ai API 配置
const KIE_API_URL = "https://kieai.erweima.ai/api/v1/gpt4o-image/record-info";
const KIE_API_KEY = process.env.KIE_API_KEY || "";

export async function GET(req: Request, { params }: { params: { taskId: string } }) {
  try {
    const taskId = params.taskId;
    
    if (!taskId) {
      return NextResponse.json(
        { error: "Task ID is required" },
        { status: 400 }
      );
    }

    // 添加缓存控制头
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };

    // 从Kie.ai API获取任务详情
    const response = await fetch(`${KIE_API_URL}?taskId=${taskId}`, {
      method: "GET",
      headers: {
        "Accept": "application/json",
        "Authorization": `Bearer ${KIE_API_KEY}`,
        ...headers
      }
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Kie.ai API error:", responseData);
      return NextResponse.json(
        { error: responseData.message || "Failed to get task info" },
        { status: response.status }
      );
    }

    // 如果任务完成，更新数据库
    if (responseData.data?.status === "SUCCESS" && 
        responseData.data?.response?.resultUrls?.length > 0) {
      try {
        const userUuid = await getUserUuid();
        const supabase = getSupabaseClient();
        await supabase
          .from("4o_generations")
          .update({
            status: responseData.data.status,
            completed_at: new Date().toISOString()
          })
          .eq("task_id", taskId)
          .eq("user_uuid", userUuid);
      } catch (error) {
        console.error("Error updating database:", error);
        // 继续执行，不影响返回结果
      }
    }

    // 直接返回 Kie.ai API 的响应
    return NextResponse.json(responseData, { headers });
    
  } catch (error) {
    console.error("Error getting task info:", error);
    return NextResponse.json(
      { error: "Failed to get task info" },
      { status: 500 }
    );
  }
}