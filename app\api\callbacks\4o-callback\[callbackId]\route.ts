import { NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";
import { v4 as uuidv4 } from "uuid";

export const runtime = 'edge';

export async function POST(req: Request, { params }: { params: { callbackId: string } }) {
  try {
    const callbackId = params.callbackId;
    
    if (!callbackId) {
      return NextResponse.json(
        { error: "Callback ID is required" },
        { status: 400 }
      );
    }

    const data = await req.json();
    console.log(`Received 4o callback for ID ${callbackId}:`, data);

    // 检查回调数据
    if (!data || !data.imageUrl) {
      return NextResponse.json(
        { error: "Invalid callback data" },
        { status: 400 }
      );
    }

    // 更新数据库中的任务状态
    const supabase = getSupabaseClient();
    const { error } = await supabase
      .from("4o_generations")
      .update({
        status: "completed",
        completed_at: new Date().toISOString(),
        generated_image_url: data.imageUrl
      })
      .eq("callback_id", callbackId);

    if (error) {
      console.error("Error updating 4o generation record:", error);
      return NextResponse.json(
        { error: "Failed to update generation record" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Callback processed successfully"
    });
  } catch (error) {
    console.error("Error processing 4o callback:", error);
    return NextResponse.json(
      { error: "Failed to process callback" },
      { status: 500 }
    );
  }
} 