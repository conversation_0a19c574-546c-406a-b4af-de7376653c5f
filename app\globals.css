@tailwind base;
@tailwind components;
@tailwind utilities;

@import "theme.css";

html {
  scroll-behavior: smooth;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-attachment: fixed;
    background-size: 200px;
    background-blend-mode: overlay;
    background-opacity: 0.05;
    background: linear-gradient(135deg, var(--photo-restore-gradient-from), var(--photo-restore-gradient-to));
  }

  /* 确保数字显示正确 */
  .numeric-content, 
  [class*="price"], 
  [class*="credits"],
  [class*="label"],
  [class*="percent"],
  [class*="progress"] {
    font-family: Arial, Helvetica, sans-serif;
    font-variant-numeric: tabular-nums;
  }

  :root {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--background);
    --sidebar-accent-foreground: var(--primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
    
    /* 老照片修复背景色 */
    --photo-restore-gradient-from: #e6f2ff; /* 浅蓝色 */
    --photo-restore-gradient-to: #ffffff; /* 白色 */
  }
  .dark {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
    
    /* 深色模式下的老照片修复背景色 */
    --photo-restore-gradient-from: #1a3a5f; /* 深蓝色 */
    --photo-restore-gradient-to: #2c3e50; /* 深灰蓝色 */
  }
}

/* Ghibli Style UI Elements */
@layer components {
  /* Rounded corners and shadows, simulating hand-drawn style */
  .ghibli-card {
    @apply rounded-xl border-2 border-primary/20 bg-card/80 backdrop-blur-sm 
           shadow-[0_10px_20px_rgba(0,0,0,0.1),_inset_0_-3px_0_rgba(0,0,0,0.1)];
  }
  
  /* Ghibli style button */
  .ghibli-button {
    @apply bg-primary text-primary-foreground rounded-full px-6 py-2
           shadow-[0_4px_0_rgba(0,0,0,0.1)] hover:shadow-[0_2px_0_rgba(0,0,0,0.1)]
           hover:translate-y-[2px] transition-all duration-200
           border-2 border-primary-foreground/10;
  }
  
  /* Ghibli style input */
  .ghibli-input {
    @apply rounded-xl border-2 border-primary/20 bg-background/50 p-3
           focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all
           backdrop-blur-sm;
  }
  
  /* Ghibli style heading */
  .ghibli-heading {
    @apply font-bold text-foreground/90 drop-shadow-sm;
  }
  
  /* 老照片修复页面背景 */
  .photo-restore-bg {
    background: linear-gradient(135deg, var(--photo-restore-gradient-from), var(--photo-restore-gradient-to));
  }
  
  /* 页面区块背景 */
  .photo-restore-section {
    @apply relative;
  }
  
  .photo-restore-section::before {
    content: "";
    @apply absolute inset-0 -z-10 photo-restore-bg opacity-50;
  }
  
  /* 卡片背景 */
  .photo-restore-card {
    @apply bg-white/70 backdrop-blur-sm border border-blue-100 rounded-xl shadow-lg;
  }
  
  /* 头部背景 */
  .photo-restore-header {
    @apply bg-white/80 backdrop-blur-md border-b border-blue-100 shadow-sm;
  }
  
  /* 底部背景 */
  .photo-restore-footer {
    @apply bg-blue-50/50 backdrop-blur-sm border-t border-blue-100;
  }
}
