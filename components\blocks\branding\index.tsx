import { Section as SectionType } from "@/types/blocks/section";

export default function Branding({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="photo-restore-section py-16">
      <div className="container px-4 mx-auto">
        <div className="photo-restore-card p-8 flex flex-col items-center gap-8">
          <h2 className="text-3xl font-semibold text-center text-gray-800">
            {section.title}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 w-full">
            {section.items?.map((item, idx) => {
              if (item.image) {
                return (
                  <div key={idx} className="flex flex-col items-center gap-4 group">
                    <div className="relative overflow-hidden rounded-lg shadow-lg bg-white/50 p-3 w-full aspect-square flex items-center justify-center transition-all duration-300 group-hover:shadow-xl">
                      <img
                        src={item.image.src}
                        alt={item.image.alt || item.title}
                        className="max-h-full max-w-full object-contain transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <p className="text-base font-medium text-center text-gray-800">{item.title}</p>
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
