import { NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";

export const runtime = 'edge';

export async function GET(req: Request, { params }: { params: { taskId: string } }) {
  try {
    const taskId = params.taskId;
    
    if (!taskId) {
      return NextResponse.json(
        { error: "Task ID is required" },
        { status: 400 }
      );
    }

    const userUuid = await getUserUuid();
    
    // 从数据库中查询任务状态
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from("4o_generations")
      .select("*")
      .eq("task_id", taskId)
      .eq("user_uuid", userUuid)
      .single();

    if (error || !data) {
      console.error("Error fetching 4o generation record:", error);
      return NextResponse.json(
        { error: "Task not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      taskId: data.task_id,
      status: data.status,
      originalImageUrl: data.original_image_url,
      generatedImageUrl: data.generated_image_url,
      prompt: data.prompt,
      createdAt: data.created_at,
      completedAt: data.completed_at
    });
  } catch (error) {
    console.error("Error getting task status:", error);
    return NextResponse.json(
      { error: "Failed to get task status" },
      { status: 500 }
    );
  }
} 