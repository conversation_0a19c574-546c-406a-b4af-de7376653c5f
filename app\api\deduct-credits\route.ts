import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { decreaseCredits, CreditsTransType, getUserCredits } from "@/services/credit";

export const runtime = "edge";

export async function POST(req: Request) {
  try {
    const { credits, taskId } = await req.json();
    
    if (!credits || typeof credits !== 'number' || credits <= 0) {
      return NextResponse.json(
        { error: "Valid credits amount is required" },
        { status: 400 }
      );
    }

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "User not authenticated" },
        { status: 401 }
      );
    }

    // Check if user has enough credits
    const userCredits = await getUserCredits(userUuid);
    if (userCredits.left_credits < credits) {
      return NextResponse.json(
        { 
          error: "insufficient_credits",
          message: `Insufficient credits. Required: ${credits}, Available: ${userCredits.left_credits}`
        },
        { status: 402 }
      );
    }

    // If taskId is provided, check if credits have already been deducted for this task
    // This uses a localStorage marker to avoid duplicate deductions
    // In a production environment, this should be checked using a database record
    if (taskId) {
      // In a real application, this should query credit history in the database
      // Simplified handling here, assuming if frontend sent taskId, it has already checked
      console.log(`Processing credits deduction for task ${taskId}`);
    }

    // Deduct credits
    try {
      // Adjust according to the actual parameters required by decreaseCredits function
      await decreaseCredits({
        user_uuid: userUuid,
        trans_type: CreditsTransType.ImageGeneration,
        credits: credits
      });
      console.log("Credits deducted successfully:", credits);
      
      // Get updated credit balance
      const updatedCredits = await getUserCredits(userUuid);
      
      return NextResponse.json({
        success: true,
        message: "Credits deducted successfully",
        creditsLeft: updatedCredits.left_credits
      });
    } catch (error) {
      console.error("Failed to deduct credits:", error);
      return NextResponse.json(
        { error: "Failed to deduct credits", details: (error as Error).message || String(error) },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error processing request:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}