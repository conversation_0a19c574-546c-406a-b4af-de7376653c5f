import { NextResponse } from "next/server";
import Replicate from "replicate";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from 'uuid';
import { getUserUuid } from "@/services/user";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { insertCredit } from "@/models/credit";
import { getSupabaseClient } from "@/models/db";
import { findUserByUuid } from "@/models/user";

export const runtime = 'edge';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// 初始化 S3 客户端 (R2 兼容 S3 API)
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

export async function POST(req: Request) {
  try {
    const { image } = await req.json();

    if (!image) {
      return NextResponse.json(
        { error: "Image is required" },
        { status: 400 }
      );
    }

    // 处理base64图片数据
    if (image.startsWith('data:image/')) {
      try {
        // 从base64数据中提取实际的图片内容
        const base64Data = image.split(',')[1];
        
        // 使用Uint8Array代替Buffer，在Edge Runtime中更兼容
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        
        // 生成唯一的文件名
        const fileExtension = image.split(';')[0].split('/')[1];
        const fileName = `${uuidv4()}.${fileExtension}`;
        
        // 上传到 R2 (用于存储)
        const command = new PutObjectCommand({
          Bucket: process.env.STORAGE_BUCKET || "",
          Key: fileName,
          Body: bytes,
          ContentType: `image/${fileExtension}`,
        });

        await s3Client.send(command);

        // 构建图片URL - 使用自定义域名
        const imageUrl = `${process.env.STORAGE_DOMAIN}/${fileName}`;
        console.log("Image uploaded to R2:", imageUrl); // 添加日志

        try {
          // 1. 使用 molmo-7b 模型进行图像描述
          const description = await replicate.run(
            "zsxkib/molmo-7b:76ebd700864218a4ca97ac1ccff068be7222272859f9ea2ae1dd4ac073fa8de8",
            {
              input: {
                image: image,
                text: "Summarize the content of the picture and write it as a prompt for me. ",
                
              }
            }
          );

          console.log("Image description:", description); // 添加日志

          // 处理描述文本，使其连贯
          let processedDescription: any = description;
          if (Array.isArray(description)) {
            processedDescription = description.join(" ");
          }
          // 清理多余的逗号和空格
          if (typeof processedDescription === 'string') {
            processedDescription = processedDescription
              .replace(/\s*,\s*/g, ", ") // 确保逗号后有一个空格
              .replace(/\s{2,}/g, " ")   // 删除多余空格
              .replace(/\.\s*,/g, ".")   // 修复句号后跟逗号的情况
              .trim();                   // 去除首尾空格
          } else if (processedDescription && typeof processedDescription === 'object') {
            // 如果是对象但不是数组，尝试转换为字符串
            processedDescription = JSON.stringify(processedDescription);
          }

          // 2. 使用 Flux 模型生成图片
          const prompt = `KEVINSTYLE, studio ghibli, soft pastel tones, fantasy landscape, anime style, KEVINSTYLE, ${processedDescription}, KEVINSTYLE`;
          console.log("Generation prompt:", prompt); // 添加日志

          try {
            const generatedImage = await replicate.run(
              "lucataco/flux-dev-lora:091495765fa5ef2725a175a57b276ec30dc9d39c22d30410f2ede68a3eab66b3",
              {
                input: {
                  prompt: prompt,
                  image: imageUrl,
                  aspect_ratio: "1:1",
                  num_inference_steps: 28,
                  hf_lora: "braingatts/ghis",
                  lora_scale: 1,
                  prompt_strength: 0.8,
                  num_outputs: 1,
                  guidance_scale: 3.5
                }
              }
            );

            // 处理生成的图片流
            let generatedImageUrl = null;
            if (Array.isArray(generatedImage) && generatedImage.length > 0) {
              if (generatedImage[0] instanceof ReadableStream) {
                // 如果是流，我们需要将其转换为 base64
                const reader = generatedImage[0].getReader();
                const chunks = [];
                
                while (true) {
                  const { done, value } = await reader.read();
                  if (done) break;
                  chunks.push(value);
                }
                
                const concatenated = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
                let offset = 0;
                for (const chunk of chunks) {
                  concatenated.set(chunk, offset);
                  offset += chunk.length;
                }
                
                // 将生成的图片保存到 R2
                const generatedFileName = `generated-${uuidv4()}.png`;
                const uploadCommand = new PutObjectCommand({
                  Bucket: process.env.STORAGE_BUCKET || "",
                  Key: generatedFileName,
                  Body: concatenated,
                  ContentType: 'image/png',
                });

                await s3Client.send(uploadCommand);
                generatedImageUrl = `${process.env.STORAGE_DOMAIN}/${generatedFileName}`;
              } else {
                // 如果不是流，可能直接是 URL
                generatedImageUrl = generatedImage[0];
              }
            }
            
            console.log("Generated image URL:", generatedImageUrl); // 添加日志

            // Get user info for saving nickname and email
            const userUuid = await getUserUuid();
            const userInfo = await findUserByUuid(userUuid);

            // Save generation record with user details
            const supabase = getSupabaseClient();
            await supabase.from("generations").insert({
              user_uuid: userUuid,
              nickname: userInfo?.nickname || "",
              email: userInfo?.email || "",
              image_created_url: generatedImageUrl,
              image_created_at: new Date().toISOString()
            });

            // Deduct credits after successful generation
            await decreaseCredits({
              user_uuid: await getUserUuid(),
              trans_type: CreditsTransType.ImageGeneration,
              credits: 3
            });

            return NextResponse.json({ 
              description: processedDescription,
              prompt: prompt,
              originalImage: imageUrl,
              generatedImage: generatedImageUrl
            });
          } catch (error) {
            console.error("Replicate API error:", error);
            // 检查是否是NSFW内容错误
            const errorMessage = error instanceof Error ? error.message : String(error);
            if (errorMessage.toLowerCase().includes('nsfw')) {
              return NextResponse.json(
                { error: "NSFW content detected. Please try a different image or prompt." },
                { status: 422 }
              );
            }
            throw new Error("Failed to process image with Replicate");
          }
        } catch (error) {
          console.error("Replicate API error:", error);
          throw new Error("Failed to process image with Replicate");
        }
      } catch (error) {
        console.error("Image upload error:", error);
        throw new Error("Failed to process image");
      }
    } else {
      return NextResponse.json(
        { error: "Invalid image format" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error analyzing image:", error);
    return NextResponse.json(
      { error: "Failed to analyze image" },
      { status: 500 }
    );
  }
} 